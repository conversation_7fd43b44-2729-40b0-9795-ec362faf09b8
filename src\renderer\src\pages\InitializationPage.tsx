import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { useNavigate } from 'react-router-dom'
import { cn } from '@/lib/utils'

// UI 组件导入
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'

// 图标导入
import {
  Sparkles,
  FolderOpen,
  RefreshCw,
  Info,
  X,
  CheckCircle2,
  AlertCircle,
  Clock,
  Loader2,
  ChevronRight,
  Settings,
  Database,
  Key,
  MessageSquare
} from 'lucide-react'

// 使用主进程定义的类型
interface InitializationStep {
  step: string
  status: 'pending' | 'in_progress' | 'success' | 'error' | 'waiting_user_input'
  progress: number
  title: string
  description: string
  error?: string
  canRetry?: boolean
  userAction?: string
}

interface InitializationState {
  currentStep: string
  steps: Record<string, InitializationStep>
  overallProgress: number
  isCompleted: boolean
  canExit: boolean
}

const InitializationPage: React.FC = () => {
  // 响应式状态
  const [state, setState] = useState<InitializationState | null>(null)
  const [isRetrying, setIsRetrying] = useState(false)
  const [showDiagnostics, setShowDiagnostics] = useState(false)
  const [diagnosticsReport, setDiagnosticsReport] = useState('')
  const navigate = useNavigate()

  // 检查是否有错误
  const hasError = useMemo(() => {
    if (!state?.steps) return false
    return Object.values(state.steps).some((step) => step.status === 'error')
  }, [state?.steps])

  // 获取步骤图标
  const getStepIcon = useCallback((stepKey: string, status: string) => {
    const iconProps = { className: 'w-5 h-5' }

    switch (stepKey) {
      case 'checking_wechat':
        return <MessageSquare {...iconProps} />
      case 'getting_key':
        return <Key {...iconProps} />
      case 'selecting_workdir':
        return <FolderOpen {...iconProps} />
      case 'decrypting_database':
        return <Database {...iconProps} />
      case 'starting_service':
        return <Settings {...iconProps} />
      default:
        return <Sparkles {...iconProps} />
    }
  }, [])

  // 获取状态图标
  const getStatusIcon = useCallback((status: string) => {
    switch (status) {
      case 'in_progress':
        return <Loader2 className="w-4 h-4 animate-spin" />
      case 'success':
        return <CheckCircle2 className="w-4 h-4 text-green-500" />
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />
      case 'waiting_user_input':
        return <Clock className="w-4 h-4 text-amber-500" />
      default:
        return <div className="w-4 h-4 rounded-full bg-muted" />
    }
  }, [])

  // 获取状态徽章样式
  const getStatusBadge = useCallback((status: string) => {
    switch (status) {
      case 'in_progress':
        return { variant: 'default' as const, text: '进行中' }
      case 'success':
        return { variant: 'secondary' as const, text: '已完成' }
      case 'error':
        return { variant: 'destructive' as const, text: '失败' }
      case 'waiting_user_input':
        return { variant: 'outline' as const, text: '等待操作' }
      default:
        return { variant: 'outline' as const, text: '等待中' }
    }
  }, [])

  // 获取当前步骤索引
  const getCurrentStepIndex = useCallback(() => {
    if (!state?.steps) return 0
    const steps = Object.keys(state.steps)
    const currentStepKey = state.currentStep
    return steps.indexOf(currentStepKey)
  }, [state?.steps, state?.currentStep])

  // 获取当前步骤信息
  const getCurrentStepInfo = useCallback(() => {
    if (!state?.steps || !state?.currentStep) return null
    return state.steps[state.currentStep]
  }, [state?.steps, state?.currentStep])

  // 重试整个初始化流程
  const retryInitialization = useCallback(async () => {
    setIsRetrying(true)
    try {
      // 重新启动初始化流程
      await window.api.initialization.start()
    } catch (error) {
      console.error('重试初始化失败:', error)
    } finally {
      setIsRetrying(false)
    }
  }, [])

  // 刷新诊断信息
  const refreshDiagnostics = useCallback(async () => {
    try {
      const result = await window.api.initialization.getDiagnostics()
      setDiagnosticsReport(result.report || JSON.stringify(result, null, 2))
    } catch (error) {
      console.error('获取诊断信息失败:', error)
      setDiagnosticsReport('获取诊断信息失败: ' + error)
    }
  }, [])

  // 选择工作目录
  const selectWorkDir = useCallback(async () => {
    try {
      const result = await window.api.initialization.selectWorkDir()
      if (result.success) {
        console.log('工作目录选择成功:', result.workDir)
      } else {
        console.error('选择目录失败:', result.error)
      }
    } catch (error) {
      console.error('选择目录失败:', error)
    }
  }, [])

  // 获取诊断信息
  const getDiagnostics = useCallback(async () => {
    try {
      const result = await window.api.initialization.getDiagnostics()
      if (result.success) {
        setDiagnosticsReport(result.report || '无诊断信息')
      } else {
        setDiagnosticsReport(`获取诊断信息失败: ${result.error}`)
      }
    } catch (error) {
      setDiagnosticsReport(`获取诊断信息失败: ${error}`)
    }
  }, [])

  // 生命周期钩子
  useEffect(() => {
    // 获取诊断信息
    getDiagnostics()

    // 注册事件监听器
    window.api.initialization.onStateChanged((newState: InitializationState) => {
      setState(newState)
    })

    window.api.initialization.onCompleted(() => {
      // 初始化完成后导航到主应用
      setTimeout(() => {
        navigate('/')
      }, 1500)
    })

    window.api.initialization.onError((error) => {
      console.error('初始化错误:', error)
    })

    // 启动初始化
    const startInitialization = async (): Promise<void> => {
      try {
        await window.api.initialization.start()
      } catch (error) {
        console.error('启动初始化失败:', error)
      }
    }

    startInitialization()

    // 清理函数
    return () => {
      window.api.initialization.removeAllListeners()
    }
  }, [navigate, getDiagnostics])

  return (
    <div className="min-h-screen bg-background">
      {/* 主容器 */}
      <div className="flex flex-col min-h-screen">
        {/* 头部区域 */}
        <div className="flex-shrink-0 pt-12 pb-8">
          <div className="container max-w-4xl px-4 mx-auto">
            <div className="space-y-6 text-center">
              {/* Logo 区域 */}
              <div className="flex items-center justify-center w-16 h-16 mx-auto shadow-lg rounded-2xl bg-primary">
                <Sparkles className="w-8 h-8 text-primary-foreground" />
              </div>

              {/* 标题区域 */}
              <div className="space-y-2">
                <h1 className="text-3xl font-bold text-foreground sm:text-4xl">
                  欢迎使用 EchoSoul
                </h1>
                <p className="max-w-2xl mx-auto text-lg text-muted-foreground">
                  正在为您初始化应用环境，请稍候...
                </p>
              </div>

              {/* 总体进度 */}
              {state && (
                <div className="max-w-md mx-auto space-y-2">
                  <div className="flex justify-between text-sm text-muted-foreground">
                    <span>初始化进度</span>
                    <span>{state.overallProgress}%</span>
                  </div>
                  <Progress value={state.overallProgress} className="h-2" />
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 px-4 pb-8">
          <div className="container max-w-4xl mx-auto">
            {/* 步骤列表 */}
            {state?.steps && (
              <div className="space-y-4">
                {Object.entries(state.steps).map(([stepKey, stepInfo]) => {
                  const statusBadge = getStatusBadge(stepInfo.status)
                  const isActive = state.currentStep === stepKey
                  const isCompleted = stepInfo.status === 'success'
                  const hasError = stepInfo.status === 'error'
                  const needsUserAction = stepInfo.status === 'waiting_user_input'

                  return (
                    <Card
                      key={stepKey}
                      className={cn(
                        'transition-all duration-300',
                        isActive && 'ring-2 ring-primary shadow-lg',
                        isCompleted && 'bg-green-50 border-green-200',
                        hasError && 'bg-red-50 border-red-200',
                        needsUserAction && 'bg-amber-50 border-amber-200'
                      )}
                    >
                      <CardContent className="p-6">
                        <div className="flex items-center gap-4">
                          {/* 步骤图标 */}
                          <div
                            className={cn(
                              'flex items-center justify-center w-12 h-12 rounded-xl transition-colors',
                              isActive && 'bg-primary text-primary-foreground',
                              isCompleted && 'bg-green-500 text-white',
                              hasError && 'bg-red-500 text-white',
                              needsUserAction && 'bg-amber-500 text-white',
                              !isActive &&
                                !isCompleted &&
                                !hasError &&
                                !needsUserAction &&
                                'bg-muted text-muted-foreground'
                            )}
                          >
                            {getStepIcon(stepKey, stepInfo.status)}
                          </div>

                          {/* 步骤信息 */}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-3 mb-2">
                              <h3 className="font-semibold text-foreground">{stepInfo.title}</h3>
                              <Badge variant={statusBadge.variant}>{statusBadge.text}</Badge>
                            </div>
                            <p className="mb-2 text-sm text-muted-foreground">
                              {stepInfo.description}
                            </p>

                            {/* 进度条 */}
                            {stepInfo.status === 'in_progress' && (
                              <div className="space-y-1">
                                <div className="flex justify-between text-xs text-muted-foreground">
                                  <span>进度</span>
                                  <span>{stepInfo.progress}%</span>
                                </div>
                                <Progress value={stepInfo.progress} className="h-1.5" />
                              </div>
                            )}

                            {/* 错误信息 */}
                            {hasError && stepInfo.error && (
                              <div className="p-2 mt-2 bg-red-100 border border-red-200 rounded-md">
                                <p className="text-sm text-red-700">{stepInfo.error}</p>
                              </div>
                            )}
                          </div>

                          {/* 状态图标 */}
                          <div className="flex-shrink-0">{getStatusIcon(stepInfo.status)}</div>
                        </div>
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            )}
          </div>
        </div>

        {/* 操作按钮区域 */}
        <div className="flex-shrink-0 px-4 pb-8">
          <div className="container max-w-4xl mx-auto">
            <div className="flex flex-col items-center justify-center gap-4 sm:flex-row">
              {/* 重试按钮 */}
              {hasError && (
                <Button
                  disabled={isRetrying}
                  variant="outline"
                  size="lg"
                  onClick={retryInitialization}
                >
                  <RefreshCw className={cn('w-5 h-5 mr-2', isRetrying && 'animate-spin')} />
                  {isRetrying ? '重试中...' : '重试初始化'}
                </Button>
              )}

              {/* 选择目录按钮 */}
              {state?.steps &&
                Object.values(state.steps).some(
                  (step) =>
                    step.status === 'waiting_user_input' && step.step === 'selecting_workdir'
                ) && (
                  <Button variant="default" size="lg" onClick={selectWorkDir}>
                    <FolderOpen className="w-5 h-5 mr-2" />
                    选择工作目录
                  </Button>
                )}
            </div>
          </div>
        </div>

        {/* 诊断信息按钮 - 固定在右下角 */}
        <div className="fixed z-30 bottom-6 right-6">
          <Button
            variant="outline"
            size="sm"
            className="shadow-lg backdrop-blur-sm"
            onClick={() => setShowDiagnostics(!showDiagnostics)}
          >
            <Info className="w-4 h-4 mr-2" />
            <span className="text-sm font-medium">系统诊断</span>
          </Button>
        </div>

        {/* 诊断信息侧边栏 */}
        {showDiagnostics && (
          <div
            className={cn(
              'fixed inset-y-0 right-0 z-50 flex flex-col transition-transform duration-300 ease-out transform border-l w-96 bg-background/95 backdrop-blur-xl border-border',
              showDiagnostics ? 'translate-x-0' : 'translate-x-full'
            )}
          >
            {/* 头部 */}
            <div className="flex items-center justify-between p-6 border-b border-border">
              <div className="flex items-center gap-3">
                <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-primary/10">
                  <Info className="w-4 h-4 text-primary" />
                </div>
                <h3 className="text-lg font-semibold text-foreground">系统诊断</h3>
              </div>
              <Button variant="ghost" size="sm" onClick={() => setShowDiagnostics(false)}>
                <X className="w-4 h-4" />
              </Button>
            </div>

            {/* 内容区域 */}
            <div className="flex-1 p-6 overflow-y-auto">
              <div className="space-y-4">
                {/* 诊断报告 */}
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="flex items-center gap-2 text-sm">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                      诊断报告
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="p-3 overflow-y-auto font-mono text-xs leading-relaxed break-words whitespace-pre-wrap border rounded-lg text-muted-foreground max-h-64 bg-muted/50">
                      {diagnosticsReport || '暂无诊断信息'}
                    </div>
                  </CardContent>
                </Card>

                {/* 当前状态 */}
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="flex items-center gap-2 text-sm">
                      <div className="w-2 h-2 rounded-full bg-primary animate-pulse" />
                      当前状态
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {state?.currentStep && (
                        <div className="flex items-center justify-between p-2 rounded-lg bg-muted/50">
                          <span className="text-xs text-muted-foreground">当前步骤</span>
                          <span className="text-xs font-medium text-foreground">
                            {state.steps[state.currentStep]?.title}
                          </span>
                        </div>
                      )}
                      {state?.steps && (
                        <div className="flex items-center justify-between p-2 rounded-lg bg-muted/50">
                          <span className="text-xs text-muted-foreground">总进度</span>
                          <span className="text-xs font-medium text-foreground">
                            {getCurrentStepIndex() + 1} / {Object.keys(state.steps).length}
                          </span>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* 错误信息 */}
                {hasError && (
                  <Card className="border-red-200 bg-red-50">
                    <CardHeader className="pb-3">
                      <CardTitle className="flex items-center gap-2 text-sm text-red-700">
                        <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
                        错误信息
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {state?.steps &&
                          Object.entries(state.steps).map(([key, step]) => {
                            if (step.status === 'error') {
                              return (
                                <div
                                  key={key}
                                  className="p-3 bg-red-100 border border-red-200 rounded-lg"
                                >
                                  <div className="text-xs font-medium text-red-800">
                                    {step.title}
                                  </div>
                                  <div className="mt-2 text-xs leading-relaxed text-red-700">
                                    {step.error || step.description || '未知错误'}
                                  </div>
                                </div>
                              )
                            }
                            return null
                          })}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>

            {/* 底部操作 */}
            <div className="flex-shrink-0 border-t border-border bg-muted/30">
              <div className="p-4">
                {/* 操作提示 */}
                <div className="mb-3 text-center">
                  <p className="text-xs text-muted-foreground">
                    {hasError ? '检测到错误，可以重试初始化' : '诊断信息实时更新'}
                  </p>
                </div>

                {/* 按钮组 */}
                <div className="flex gap-2">
                  {!hasError ? (
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1 text-green-600 border-green-200 bg-green-50 hover:bg-green-100"
                      disabled
                    >
                      <div className="w-2 h-2 mr-2 bg-green-500 rounded-full animate-pulse" />
                      <span className="font-medium">运行正常</span>
                    </Button>
                  ) : (
                    <Button
                      variant="destructive"
                      size="sm"
                      className="flex-1"
                      onClick={retryInitialization}
                    >
                      <span className="font-medium">重试初始化</span>
                    </Button>
                  )}

                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1"
                    onClick={refreshDiagnostics}
                  >
                    <RefreshCw className="w-3 h-3 mr-2" />
                    <span className="font-medium">刷新诊断</span>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 遮罩层 */}
        {showDiagnostics && (
          <div
            className="fixed inset-0 z-40 bg-black/20 backdrop-blur-sm"
            onClick={() => setShowDiagnostics(false)}
          />
        )}
      </div>
    </div>
  )
}

export default InitializationPage
